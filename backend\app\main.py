from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, Form, HTTPException, Body
from fastapi.responses import JSO<PERSON>esponse, FileResponse
from .models import Questionnaire, QuestionnaireResponse
from .utils import parse_markdown_to_questionnaire
import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

DOCS_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../docs'))
DATA_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/questionnaires'))
RESPONSE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../data/responses'))
PUBLIC_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../frontend/public'))

os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(RESPONSE_DIR, exist_ok=True)

@app.get("/")
def read_root():
    """
    Root endpoint for health check.

    Returns:
        dict: Welcome message.
    """
    return {"message": "Welcome to the Marketing Research Tool API"}

# Questionnaire endpoints

@app.post("/api/questionnaire/generate")
def generate_questionnaire(filename: str = Form(None), file: UploadFile = File(None), title: str = Form("Questionnaire")):
    """
    Generate a questionnaire from a Markdown file (by filename or upload).
    """
    if filename:
        file_path = os.path.join(DOCS_DIR, filename)
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="File not found.")
        with open(file_path, 'r', encoding='utf-8') as f:
            md_text = f.read()
    elif file:
        md_text = file.file.read().decode('utf-8')
    else:
        raise HTTPException(status_code=400, detail="No file or filename provided.")
    questionnaire = parse_markdown_to_questionnaire(md_text, title=title)
    return questionnaire.dict()

@app.post("/api/questionnaire/save")
def save_questionnaire(q: Questionnaire):
    """
    Save a questionnaire to the data/questionnaires directory as JSON.
    """
    file_id = q.title.replace(' ', '_').lower()
    file_path = os.path.join(DATA_DIR, f"{file_id}.json")
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(q.dict(), f, ensure_ascii=False, indent=2)
    return {"success": True, "file": f"{file_id}.json"}

@app.get("/api/questionnaire/list")
def list_questionnaires():
    """
    List all saved questionnaires.
    """
    files = [f for f in os.listdir(DATA_DIR) if f.endswith('.json')]
    return {"files": files}

@app.get("/api/yaml/list")
def list_yaml_questionnaires():
    """
    List all YAML questionnaire files in the public directory.
    """
    files = [f for f in os.listdir(PUBLIC_DIR) if f.endswith('.yaml')]
    return {"files": files}

@app.get("/api/yaml/{file_name}")
def get_yaml_questionnaire(file_name: str):
    """
    Get a specific YAML questionnaire file.
    """
    file_path = os.path.join(PUBLIC_DIR, file_name)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="YAML file not found.")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            yaml_content = yaml.safe_load(f)
        return yaml_content
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading YAML file: {str(e)}")

@app.get("/api/questionnaire/{file_id}")
def get_questionnaire(file_id: str):
    """
    Get a saved questionnaire by file id.
    """
    file_path = os.path.join(DATA_DIR, f"{file_id}.json")
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Questionnaire not found.")
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data

# Response endpoints

@app.post("/api/responses/save")
def save_response(response: Dict[str, Any] = Body(...)):
    """
    Save a questionnaire response to the data/responses directory as JSON.
    
    Args:
        response: A dictionary containing questionnaire responses
        
    Returns:
        A success message with the filename
    """
    # Generate a unique ID for the response
    response_id = str(uuid.uuid4())
    timestamp = datetime.now().isoformat()
    
    # Add metadata
    response_data = {
        "id": response_id,
        "timestamp": timestamp,
        **response
    }
    
    # Create filename based on questionnaire title and timestamp
    questionnaire_name = response.get("questionnaire", "unknown").replace(" ", "_").lower()
    filename = f"{questionnaire_name}_{response_id[:8]}.json"
    file_path = os.path.join(RESPONSE_DIR, filename)
    
    # Save the response
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(response_data, f, ensure_ascii=False, indent=2)
    
    return {"success": True, "file": filename, "id": response_id}

@app.get("/api/responses/list")
def list_responses():
    """
    List all saved questionnaire responses.
    """
    files = [f for f in os.listdir(RESPONSE_DIR) if f.endswith('.json')]
    return {"files": files}

@app.get("/api/responses/{file_id}")
def get_response(file_id: str):
    """
    Get a saved response by file id.
    """
    # Check if the file_id includes the .json extension
    if not file_id.endswith('.json'):
        file_id = f"{file_id}.json"
        
    file_path = os.path.join(RESPONSE_DIR, file_id)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Response not found.")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    return data

@app.get("/api/responses/download/{file_id}")
def download_response(file_id: str):
    """
    Download a saved response as a JSON file.
    """
    # Check if the file_id includes the .json extension
    if not file_id.endswith('.json'):
        file_id = f"{file_id}.json"
        
    file_path = os.path.join(RESPONSE_DIR, file_id)
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Response not found.")
    
    return FileResponse(
        path=file_path,
        filename=file_id,
        media_type="application/json"
    )