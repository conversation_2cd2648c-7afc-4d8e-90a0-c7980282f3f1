import React, { useState, useEffect } from 'react';
import yaml from 'js-yaml';
import axios from 'axios';
import { DateInput, RatingInput, FileUploadInput, SliderInput } from './AdvancedInputs';
import config from '../config';

/**
 * QuestionnaireLoader component
 *
 * A reusable component that loads and displays YAML questionnaires.
 * Can be embedded in any page and configured with specific questionnaires.
 */
function QuestionnaireLoader({
  defaultQuestionnaire = '',
  specificQuestionnaires = [],
  onSubmit,
  onSave,
  showLocalSave = true,
  title = "Questionnaire",
  description = "Please fill out the questionnaire below.",
  onGenerateStrategy,
  hideQuestionnaireSelector = false,
  hideGenerateStrategyButton = true
}) {
  const [filename, setFilename] = useState(defaultQuestionnaire);
  const [files, setFiles] = useState([]);
  const [questionnaire, setQuestionnaire] = useState(null);
  const [responses, setResponses] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitProgress, setSubmitProgress] = useState(0);
  const [error, setError] = useState('');
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState({});
  const [isGeneratingStrategy, setIsGeneratingStrategy] = useState(false);

  // Function to fetch available YAML files
  const fetchYamlFiles = async () => {
    try {
      setLoading(true);
      let availableFiles = [];
      
      // If specific questionnaires are provided, use only those
      if (specificQuestionnaires && specificQuestionnaires.length > 0) {
        availableFiles = specificQuestionnaires;
      } else {
        // First try to fetch from backend (only if endpoint is available)
        if (config.endpoints.yaml.list) {
          try {
            const response = await axios.get(config.endpoints.yaml.list);
            if (response.data && response.data.files) {
              availableFiles.push(...response.data.files);
            }
          } catch (err) {
            console.warn('Could not fetch files from backend:', err);
          }
        }
        
        // Then scan the public folder for YAML files
        const publicFiles = await scanPublicFolderForYamlFiles();
        
        // Add known files that might not be detected by the scanner
        const knownFiles = [
          '00-strategy-questionnaire.yaml',
          '01-spiritual-fine-jewelry-survey.yaml',
          '03-ideal-customer-profile.yaml',
          '04-customer-acquisition-strategy.yaml',
          'lead-generation_strategy-questionnaire-01.yaml'
        ];
        
        // Combine all sources and remove duplicates
        availableFiles = [...new Set([...availableFiles, ...publicFiles, ...knownFiles])];
      }
      
      console.log('All available files:', availableFiles);
      setFiles(availableFiles);
      setLoading(false);
      
      // If default questionnaire is set and not already loaded, load it
      if (defaultQuestionnaire && !questionnaire) {
        loadQuestionnaire(defaultQuestionnaire);
      }
    } catch (err) {
      console.error('Error fetching YAML files:', err);
      setError('Failed to load available questionnaires');
      setLoading(false);
    }
  };
  
  // Fetch available YAML files on component mount
  useEffect(() => {
    fetchYamlFiles();
  }, []);

  // Handle defaultQuestionnaire prop
  useEffect(() => {
    if (defaultQuestionnaire && !filename) {
      setFilename(defaultQuestionnaire);
    }
  }, [defaultQuestionnaire, filename]);
  
  // Function to scan the public folder for YAML files
  const scanPublicFolderForYamlFiles = async () => {
    try {
      // Get the list of files in the public directory
      const response = await fetch('/');
      const html = await response.text();
      
      // Create a temporary DOM element to parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      
      // Find all links that might be YAML files
      const links = Array.from(doc.querySelectorAll('a'));
      
      // Filter for YAML, YML, and YALM files
      const yamlFiles = links
        .map(link => link.getAttribute('href'))
        .filter(href => {
          if (!href) return false;
          const lowerHref = href.toLowerCase();
          return lowerHref.endsWith('.yaml') || lowerHref.endsWith('.yml') || lowerHref.endsWith('.yalm');
        })
        .map(href => href.split('/').pop()); // Get just the filename
      
      console.log('YAML files found:', yamlFiles);
      
      // Also try to directly fetch known files
      const knownFilesToCheck = [
        '00-strategy-questionnaire.yaml',
        '01-spiritual-fine-jewelry-survey.yaml',
        '03-ideal-customer-profile.yaml',
        '04-customer-acquisition-strategy.yaml',
        'lead-generation_strategy-questionnaire-01.yaml'
      ];
      
      for (const file of knownFilesToCheck) {
        try {
          const directResponse = await fetch(`/${file}`);
          if (directResponse.ok && !yamlFiles.includes(file)) {
            console.log(`Found ${file} via direct fetch`);
            yamlFiles.push(file);
          }
        } catch (err) {
          console.warn(`Could not fetch ${file}:`, err);
        }
      }
      
      return yamlFiles;
    } catch (err) {
      console.error('Error scanning public folder:', err);
      return [];
    }
  };

  // Load a questionnaire when filename changes
  useEffect(() => {
    if (filename) {
      loadQuestionnaire(filename);
    }
  }, [filename]);

  // Function to load a questionnaire by filename
  const loadQuestionnaire = async (file) => {
    try {
      setLoading(true);
      setError('');
      setQuestionnaire(null);
      setResponses({});
      setSaveSuccess(false);
      setSaveMessage('');
      setValidationErrors({});
      
      // Try to load from backend first (only if endpoint is available)
      if (config.endpoints.yaml.get(file)) {
        try {
          const response = await axios.get(config.endpoints.yaml.get(file));
          if (response.data) {
            setQuestionnaire(response.data);
            setLoading(false);
            return;
          }
        } catch (err) {
          console.warn(`Could not load ${file} from backend:`, err);
        }
      }
      
      // If backend fails, try to load from public folder
      try {
        const response = await fetch(`/${file}`);
        if (!response.ok) {
          throw new Error(`Failed to load ${file}`);
        }
        
        const yamlText = await response.text();
        const data = yaml.load(yamlText);
        
        if (!data || !data.title || !data.sections) {
          throw new Error('Invalid YAML format');
        }
        
        setQuestionnaire(data);
      } catch (err) {
        console.error(`Error loading ${file}:`, err);
        setError(`Failed to load questionnaire: ${err.message}`);
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error loading questionnaire:', err);
      setError('Failed to load questionnaire');
      setLoading(false);
    }
  };

  // Handle input changes
  const handleChange = (id, value, type) => {
    setResponses(prev => ({
      ...prev,
      [id]: value
    }));
    
    // Clear validation error for this field if it exists
    if (validationErrors[id]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Validate form before submission
  const validateForm = () => {
    if (!questionnaire) return false;
    
    const errors = {};
    
    questionnaire.sections.forEach(section => {
      section.questions.forEach(q => {
        if (q.required && (!responses[q.id] || 
            (Array.isArray(responses[q.id]) && responses[q.id].length === 0) ||
            responses[q.id] === '')) {
          errors[q.id] = `${q.text} is required`;
        }
      });
    });
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Save responses locally as a JSON file
  const handleLocalSave = () => {
    if (!validateForm()) {
      setSaveSuccess(false);
      setSaveMessage('Please fill out all required fields');
      return;
    }
    
    try {
      // Create a response object with metadata
      const responseData = {
        questionnaire: questionnaire.title,
        timestamp: new Date().toISOString(),
        responses: responses
      };
      
      // Convert to JSON and create a blob
      const jsonData = JSON.stringify(responseData, null, 2);
      const blob = new Blob([jsonData], { type: 'application/json' });
      
      // Create a download link and trigger it
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${filename.replace('.yaml', '')}_response_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      setSaveSuccess(true);
      setSaveMessage('Responses saved successfully');
      
      // Call the onSave callback if provided
      if (onSave) {
        onSave(responseData);
      }
    } catch (err) {
      console.error('Error saving responses:', err);
      setSaveSuccess(false);
      setSaveMessage('Failed to save responses');
    }
  };

  // Submit responses to the backend
  const handleSubmit = async () => {
    // Validate form first
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }
    
    setValidationErrors({});
    setLoading(true);
    setSubmitProgress(10); // Start progress
    
    try {
      // Prepare data to submit
      const data = {
        questionnaire: filename,
        responses: responses,
        timestamp: new Date().toISOString()
      };
      
      let serverSubmissionSuccessful = false;
      
      // Update progress
      setSubmitProgress(30);
      
      // Try to submit to the serverless function first (only if endpoint is available)
      if (config.endpoints.responses.save) {
        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setSubmitProgress(prev => Math.min(prev + 10, 90));
          }, 300);

          // Add headers to help with CORS
          const response = await axios.post(config.endpoints.responses.save, data, {
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });

          clearInterval(progressInterval);
          setSubmitProgress(95);

          console.log('Serverless function response:', response);
          serverSubmissionSuccessful = true;
        } catch (apiError) {
          console.warn('Serverless function submission failed, falling back to local storage:', apiError);
          // If API call fails, fall back to localStorage
          const savedResponses = JSON.parse(localStorage.getItem('savedResponses') || '{}');
          savedResponses[filename] = data;
          localStorage.setItem('savedResponses', JSON.stringify(savedResponses));
          console.log('Saved responses locally as fallback:', data);
        }
      } else {
        // No backend endpoint available, save directly to localStorage
        console.log('No backend endpoint available, saving to localStorage only');
        const savedResponses = JSON.parse(localStorage.getItem('savedResponses') || '{}');
        savedResponses[filename] = data;
        localStorage.setItem('savedResponses', JSON.stringify(savedResponses));
        console.log('Saved responses locally:', data);
      }
      
      // Complete progress
      setSubmitProgress(100);
      
      // Show success message
      setSaveSuccess(true);
      setSaveMessage(serverSubmissionSuccessful ? 
        'Responses submitted successfully to server!' : 
        'Responses saved locally. Server submission will be attempted later.');
      
      // Call the onSubmit callback if provided
      if (onSubmit && typeof onSubmit === 'function') {
        onSubmit(data);
      }
    } catch (err) {
      console.error('Error in submission process:', err);
      setError('Failed to submit responses. Please try again.');
    } finally {
      // Reset progress after a short delay to show completion
      setTimeout(() => {
        setSubmitProgress(0);
        setLoading(false);
      }, 500);
    }
  };
  
  // Generate strategy based on responses
  const handleGenerateStrategy = () => {
    try {
      setIsGeneratingStrategy(true);
      
      // Validate the form first
      if (!validateForm()) {
        setIsGeneratingStrategy(false);
        return;
      }
      
      // Prepare the data
      const data = {
        questionnaire: filename,
        responses: responses,
        timestamp: new Date().toISOString()
      };
      
      console.log('Generating strategy based on:', data);
      
      // If onGenerateStrategy prop is provided, call it
      if (onGenerateStrategy) {
        onGenerateStrategy(data);
      } else {
        // Default behavior - navigate to the strategy page with the responses
        // Use window.location to ensure full page refresh and state reset
        window.location.href = `/strategy?q=${encodeURIComponent(filename)}&t=${Date.now()}`;
        
        // Store the responses in sessionStorage for the strategy page to access
        sessionStorage.setItem('questionnaire_responses', JSON.stringify(data));
      }
      
      setIsGeneratingStrategy(false);
    } catch (err) {
      console.error('Error generating strategy:', err);
      setError('Failed to generate strategy');
      setIsGeneratingStrategy(false);
    }
  };

  // Render input based on question type
  const renderInput = (q) => {
    const value = responses[q.id] || '';
    
    switch (q.type) {
      case 'text':
        return (
          <input
            type="text"
            id={q.id}
            className="w-full border rounded px-3 py-2 body-text"
            value={value}
            onChange={e => handleChange(q.id, e.target.value)}
            placeholder={q.placeholder || ''}
          />
        );
        
      case 'textarea':
        return (
          <textarea
            id={q.id}
            className="w-full border rounded px-3 py-2 body-text"
            value={value}
            onChange={e => handleChange(q.id, e.target.value)}
            placeholder={q.placeholder || ''}
            rows={4}
          />
        );
        
      case 'radio':
        return (
          <div className="space-y-2">
            {q.options.map((option, i) => (
              <div key={i} className="flex items-center">
                <input
                  type="radio"
                  id={`${q.id}_${i}`}
                  name={q.id}
                  value={option}
                  checked={value === option}
                  onChange={e => handleChange(q.id, e.target.value)}
                  className="mr-2"
                />
                <label htmlFor={`${q.id}_${i}`} className="body-text">{option}</label>
              </div>
            ))}
          </div>
        );
        
      case 'checkbox':
        return (
          <div className="space-y-2">
            {q.options.map((option, i) => (
              <div key={i} className="flex items-center">
                <input
                  type="checkbox"
                  id={`${q.id}_${i}`}
                  value={option}
                  checked={Array.isArray(responses[q.id]) && responses[q.id].includes(option)}
                  onChange={e => {
                    const currentValues = Array.isArray(responses[q.id]) ? [...responses[q.id]] : [];
                    if (e.target.checked) {
                      handleChange(q.id, [...currentValues, option]);
                    } else {
                      handleChange(q.id, currentValues.filter(v => v !== option));
                    }
                  }}
                  className="mr-2"
                />
                <label htmlFor={`${q.id}_${i}`} className="body-text">{option}</label>
              </div>
            ))}
          </div>
        );
        
      case 'select':
        return (
          <select
            id={q.id}
            className="w-full border rounded px-3 py-2 body-text"
            value={value}
            onChange={e => handleChange(q.id, e.target.value)}
          >
            <option value="">-- Select an option --</option>
            {q.options.map((option, i) => (
              <option key={i} value={option}>{option}</option>
            ))}
          </select>
        );
        
      case 'date':
        return (
          <DateInput
            id={q.id}
            value={value}
            onChange={val => handleChange(q.id, val)}
          />
        );
        
      case 'rating':
        return (
          <RatingInput
            id={q.id}
            value={value}
            onChange={val => handleChange(q.id, val)}
            max={q.max || 5}
          />
        );
        
      case 'file':
        return (
          <FileUploadInput
            id={q.id}
            onChange={files => handleChange(q.id, files)}
            accept={q.accept}
            multiple={q.multiple}
          />
        );
        
      case 'slider':
        return (
          <SliderInput
            id={q.id}
            value={value || q.min || 0}
            onChange={val => handleChange(q.id, val)}
            min={q.min || 0}
            max={q.max || 100}
            step={q.step || 1}
          />
        );
        
      default:
        return (
          <input
            type="text"
            id={q.id}
            className="w-full border rounded px-3 py-2 body-text"
            value={value}
            onChange={e => handleChange(q.id, e.target.value)}
            placeholder={q.placeholder || ''}
          />
        );
    }
  };

  return (
    <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md">
      {!hideQuestionnaireSelector && (
        <>
          <h2 className="raleway-title-h2 mb-2">{title}</h2>
          <p className="body-text mb-4">{description}</p>
        </>
      )}
      
      {!hideQuestionnaireSelector && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-1">
            <label className="block raleway-menu">Select a questionnaire:</label>
            {loading && <span className="text-blue-500 text-sm animate-pulse">Loading...</span>}
          </div>
          <div className="relative">
            <select
              className="w-full border rounded px-3 py-2 body-text"
              value={filename}
              onChange={e => setFilename(e.target.value)}
              onClick={() => fetchYamlFiles()}
              onFocus={() => fetchYamlFiles()}
            >
              <option value="">-- Choose a questionnaire --</option>
              {files.map(f => (
                <option key={f} value={f}>{f}</option>
              ))}
            </select>
            <button 
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                fetchYamlFiles();
              }}
              title="Refresh questionnaire list"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>
      )}
      
      {loading && filename && <div className="body-text">Loading questionnaire...</div>}
      {error && <div className="text-red-600 mt-2 body-text">{error}</div>}
      
      {questionnaire && (
        <form className="space-y-8 mt-6" onSubmit={e => e.preventDefault()}>
          <div className="mb-4">
            <h3 className="raleway-title-h3 mb-1">{questionnaire.title}</h3>
            <p className="body-text mb-2">{questionnaire.description}</p>
          </div>
          
          {questionnaire.sections.map((section, i) => (
            <div key={i} className="mb-6 p-4 bg-white rounded shadow-sm">
              <h4 className="raleway-title-h4 mb-2">{section.title}</h4>
              <div className="space-y-4">
                {section.questions.map(q => (
                  <div key={q.id} className="mb-2 p-3 border-l-2 border-blue-100">
                    <label className="block raleway-menu mb-1">{q.text}{q.required && <span className="text-red-500">*</span>}</label>
                    {q.explanation && <p className="text-sm body-text mb-2 text-gray-500">{q.explanation}</p>}
                    {renderInput(q)}
                    {validationErrors[q.id] && (
                      <p className="text-red-500 text-sm mt-1 body-text">{validationErrors[q.id]}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
          
          {/* Progress bar */}
          {submitProgress > 0 && (
            <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4 mt-4">
              <div 
                className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out" 
                style={{ width: `${submitProgress}%` }}
              ></div>
            </div>
          )}
          
          {/* Form buttons */}
          <div className="flex flex-wrap justify-between gap-4 mt-6">
            {showLocalSave && (
              <button
                type="button"
                className="omega-nav-btn"
                onClick={handleLocalSave}
                disabled={loading}
              >
                Save Locally
              </button>
            )}
            
            <button
              type="button"
              className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? 'Submitting...' : 'Submit'}
            </button>
            
            {!hideGenerateStrategyButton && (
              <button
                type="button"
                className="px-6 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition font-medium uppercase text-sm"
                onClick={handleGenerateStrategy}
                disabled={loading || isGeneratingStrategy}
              >
                {isGeneratingStrategy ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Generating...
                  </span>
                ) : (
                  'Generate the Strategy'
                )}
              </button>
            )}
          </div>
          
          {saveSuccess && <div className="text-green-600 mt-2 body-text">{saveMessage}</div>}
          
          {Object.keys(validationErrors).length > 0 && (
            <div className="text-red-500 mt-4 p-2 border border-red-300 rounded bg-red-50">
              <p className="raleway-menu">Please fix the following errors:</p>
              <ul className="list-disc pl-5">
                {Object.keys(validationErrors).map(key => (
                  <li key={key} className="body-text">{validationErrors[key]}</li>
                ))}
              </ul>
            </div>
          )}
        </form>
      )}
    </div>
  );
}

export default QuestionnaireLoader;
