from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import json
import os
import re
import yaml
from datetime import datetime
import uvicorn

# Import utility functions
from utils import get_questionnaires_dir, get_responses_dir, get_public_dir, parse_markdown_to_questionnaire

app = FastAPI(title="Market Research Tool API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Models
class QuestionnaireResponse(BaseModel):
    success: bool
    filename: str

class ResponseMetadata(BaseModel):
    id: str
    filename: str
    title: str
    timestamp: str
    respondent: str

class ResponsesList(BaseModel):
    responses: List[ResponseMetadata]

class FilesList(BaseModel):
    files: List[str]

class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None

# Root endpoint
@app.get("/", response_model=Dict[str, Any])
async def root():
    """Root API endpoint"""
    return {
        "message": "Welcome to the Market Research Tool API",
        "endpoints": [
            "/api/questionnaire/list",
            "/api/questionnaire/{file_id}",
            "/api/questionnaire/generate/{file_id}",
            "/api/questionnaire/save",
            "/api/responses/list",
            "/api/responses/{file_id}",
            "/api/responses/download/{file_id}",
            "/api/responses/save",
            "/api/yaml"
        ]
    }

# YAML endpoints
@app.get("/api/yaml", response_model=FilesList)
async def list_yaml_files():
    """List all YAML files"""
    try:
        public_dir = get_public_dir()
        print(f"Public directory path: {public_dir}")

        if os.path.exists(public_dir):
            print(f"Files in public directory: {os.listdir(public_dir)}")
            files = [f for f in os.listdir(public_dir) if f.endswith('.yaml') or f.endswith('.yml')]
        else:
            print("Public directory not found")
            files = []

        return {"files": files}
    except Exception as e:
        print(f"Error in list_yaml_files: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/yaml/{file_name}", response_model=Dict[str, Any])
async def get_yaml_file(file_name: str):
    """Get a specific YAML file"""
    try:
        public_dir = get_public_dir()
        file_path = os.path.join(public_dir, file_name)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="YAML file not found")

        with open(file_path, 'r', encoding='utf-8') as f:
            yaml_content = yaml.safe_load(f)

        return yaml_content
    except Exception as e:
        print(f"Error in get_yaml_file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Questionnaire endpoints
@app.get("/api/questionnaire/list", response_model=FilesList)
async def list_questionnaires():
    """List all questionnaires"""
    questionnaires_dir = get_questionnaires_dir()
    files = []
    
    if os.path.exists(questionnaires_dir):
        files = [f for f in os.listdir(questionnaires_dir) if f.endswith('.json')]
    
    return {"files": files}

@app.get("/api/questionnaire/{file_id}", response_model=Dict[str, Any])
async def get_questionnaire(file_id: str):
    """Get a specific questionnaire by ID"""
    questionnaires_dir = get_questionnaires_dir()
    file_path = os.path.join(questionnaires_dir, f"{file_id}.json")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Questionnaire not found")
    
    with open(file_path, 'r') as f:
        questionnaire = json.load(f)
    
    return questionnaire

@app.get("/api/questionnaire/generate/{file_id}", response_model=Dict[str, Any])
async def generate_questionnaire(file_id: str):
    """Generate a questionnaire from a YAML file"""
    public_dir = get_public_dir()
    questionnaires_dir = get_questionnaires_dir()
    
    yaml_file = os.path.join(public_dir, f"{file_id}.yaml")
    if not os.path.exists(yaml_file):
        yaml_file = os.path.join(public_dir, f"{file_id}.yml")
        if not os.path.exists(yaml_file):
            raise HTTPException(status_code=404, detail="YAML file not found")
    
    try:
        with open(yaml_file, 'r') as f:
            yaml_content = yaml.safe_load(f)
        
        # Convert YAML to questionnaire format
        questionnaire = {
            "title": yaml_content.get("title", "Questionnaire"),
            "description": yaml_content.get("description", ""),
            "sections": []
        }
        
        for section in yaml_content.get("sections", []):
            section_data = {
                "title": section.get("title", ""),
                "questions": []
            }
            
            for question in section.get("questions", []):
                question_data = {
                    "id": question.get("id", f"q{len(section_data['questions'])+1}"),
                    "text": question.get("text", ""),
                    "type": question.get("type", "text"),
                    "required": question.get("required", False),
                    "options": question.get("options", []),
                    "explanation": question.get("explanation", "")
                }
                section_data["questions"].append(question_data)
            
            questionnaire["sections"].append(section_data)
        
        # Save the generated questionnaire
        output_file = os.path.join(questionnaires_dir, f"{file_id}.json")
        with open(output_file, 'w') as f:
            json.dump(questionnaire, f)
        
        return questionnaire
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/questionnaire/save", response_model=QuestionnaireResponse)
async def save_questionnaire(request: Request):
    """Save a questionnaire"""
    questionnaires_dir = get_questionnaires_dir()
    
    # Read request body
    questionnaire = await request.json()
    
    # Generate a filename if not provided
    if 'filename' not in questionnaire:
        title = questionnaire.get('title', 'questionnaire')
        filename = re.sub(r'[^\w\-_]', '_', title.lower()) + '.json'
    else:
        filename = questionnaire['filename']
        del questionnaire['filename']
    
    # Ensure filename has .json extension
    if not filename.endswith('.json'):
        filename += '.json'
    
    # Save the questionnaire
    file_path = os.path.join(questionnaires_dir, filename)
    with open(file_path, 'w') as f:
        json.dump(questionnaire, f)
    
    return {"success": True, "filename": filename}

# Response endpoints
@app.get("/api/responses/list", response_model=ResponsesList)
async def list_responses():
    """List all responses"""
    responses_dir = get_responses_dir()
    files = []
    
    # Get files from the responses directory
    if os.path.exists(responses_dir):
        files = [f for f in os.listdir(responses_dir) if f.endswith('.json')]
    
    # Get metadata for each response
    responses = []
    for file in files:
        try:
            file_path = os.path.join(responses_dir, file)
            with open(file_path, 'r') as f:
                response_data = json.load(f)
            
            # Extract metadata
            metadata = {
                "id": file.replace('.json', ''),
                "filename": file,
                "title": response_data.get("questionnaire_title", "Unknown"),
                "timestamp": response_data.get("timestamp", ""),
                "respondent": response_data.get("respondent", {}).get("name", "Anonymous")
            }
            responses.append(metadata)
        except Exception as e:
            print(f"Error reading response file {file}: {str(e)}")
    
    return {"responses": responses}

@app.get("/api/responses/{file_id}", response_model=Dict[str, Any])
async def get_response(file_id: str):
    """Get a specific response by ID"""
    responses_dir = get_responses_dir()
    file_path = os.path.join(responses_dir, f"{file_id}.json")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Response not found")
    
    with open(file_path, 'r') as f:
        response = json.load(f)
    
    return response

@app.get("/api/responses/download/{file_id}")
async def download_response(file_id: str):
    """Download a response"""
    responses_dir = get_responses_dir()
    file_path = os.path.join(responses_dir, f"{file_id}.json")
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Response not found")
    
    with open(file_path, 'r') as f:
        response = json.load(f)
    
    # Create a custom response with attachment header
    content = json.dumps(response, indent=2).encode()
    response = Response(content=content, media_type="application/json")
    response.headers["Content-Disposition"] = f'attachment; filename="{file_id}.json"'
    
    return response

@app.post("/api/responses/save", response_model=QuestionnaireResponse)
async def save_response(request: Request):
    """Save a response"""
    responses_dir = get_responses_dir()
    
    # Read request body
    response_data = await request.json()
    
    # Add timestamp if not present
    if 'timestamp' not in response_data:
        response_data['timestamp'] = datetime.now().isoformat()
    
    # Generate a filename if not provided
    if 'filename' not in response_data:
        questionnaire_title = response_data.get('questionnaire_title', 'response')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{re.sub(r'[^\w\-_]', '_', questionnaire_title.lower())}_{timestamp}.json"
    else:
        filename = response_data['filename']
        del response_data['filename']
    
    # Ensure filename has .json extension
    if not filename.endswith('.json'):
        filename += '.json'
    
    # Save the response
    file_path = os.path.join(responses_dir, filename)
    with open(file_path, 'w') as f:
        json.dump(response_data, f)
    
    return {"success": True, "filename": filename}

# AI API endpoints
@app.post("/api/gemini", response_model=Dict[str, Any])
async def gemini_api(request: Request):
    """Handle Gemini API requests"""
    try:
        data = await request.json()
        prompt = data.get('prompt', data.get('question', 'No prompt provided'))
        model = data.get('model', 'gemini-1.5-pro')
        
        # Here you would implement the actual Gemini API call
        # For now, we'll return a mock response
        return {
            "text": f"This is a response from the Gemini API using the {model} model.\n\nYour prompt was: \"{prompt}\"",
            "model": model,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/openai", response_model=Dict[str, Any])
async def openai_api(request: Request):
    """Handle OpenAI API requests"""
    try:
        data = await request.json()
        prompt = data.get('prompt', data.get('question', 'No prompt provided'))
        model = data.get('model', 'gpt-3.5-turbo')
        
        # Here you would implement the actual OpenAI API call
        # For now, we'll return a mock response
        return {
            "text": f"This is a response from the OpenAI API using the {model} model.\n\nYour prompt was: \"{prompt}\"",
            "model": model,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/deepseek", response_model=Dict[str, Any])
async def deepseek_api(request: Request):
    """Handle DeepSeek API requests"""
    try:
        data = await request.json()
        prompt = data.get('prompt', data.get('question', 'No prompt provided'))
        model = data.get('model', 'deepseek-chat')
        
        # Here you would implement the actual DeepSeek API call
        # For now, we'll return a mock response
        return {
            "text": f"This is a response from the DeepSeek API using the {model} model.\n\nYour prompt was: \"{prompt}\"",
            "model": model,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/anthropic", response_model=Dict[str, Any])
async def anthropic_api(request: Request):
    """Handle Anthropic API requests"""
    try:
        data = await request.json()
        prompt = data.get('prompt', data.get('question', 'No prompt provided'))
        model = data.get('model', 'claude-3-opus')
        
        # Here you would implement the actual Anthropic API call
        # For now, we'll return a mock response
        return {
            "text": f"This is a response from the Anthropic API using the {model} model.\n\nYour prompt was: \"{prompt}\"",
            "model": model,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/groq", response_model=Dict[str, Any])
async def groq_api(request: Request):
    """Handle Groq API requests"""
    try:
        data = await request.json()
        prompt = data.get('prompt', data.get('question', 'No prompt provided'))
        model = data.get('model', 'llama3-70b-8192')
        
        # Here you would implement the actual Groq API call
        # For now, we'll return a mock response
        return {
            "text": f"This is a response from the Groq API using the {model} model.\n\nYour prompt was: \"{prompt}\"",
            "model": model,
            "success": True
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=404,
        content={"error": "Not found"}
    )

@app.exception_handler(500)
async def server_error_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "details": str(exc.detail) if hasattr(exc, 'detail') else None
        }
    )

# Run the server when executed directly
if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=3001, reload=True)
