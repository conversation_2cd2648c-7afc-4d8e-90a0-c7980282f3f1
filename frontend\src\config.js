/**
 * Configuration for the application
 * This file manages environment-specific settings
 */

// Determine if we're in production based on the URL
const isProduction = window.location.hostname !== 'localhost';

// API base URL - use relative URLs in production for Vercel deployment (questionnaires, responses, etc.)
// For local development, disable backend calls by using a non-existent URL
const API_BASE_URL = isProduction ? '/api' : null;

// Railway backend URL for LLM operations
const RAILWAY_API_BASE_URL = isProduction
  ? 'https://op-market-research-tool-production-93d7.up.railway.app'
  : 'http://localhost:8000';

export default {
  API_BASE_URL,
  RAILWAY_API_BASE_URL,
  endpoints: {
    // Questionnaire endpoints (Vercel) - disabled in local development
    questionnaire: {
      list: API_BASE_URL ? `${API_BASE_URL}/questionnaire/list` : null,
      get: (id) => API_BASE_URL ? `${API_BASE_URL}/questionnaire/${id}` : null,
      save: API_BASE_URL ? `${API_BASE_URL}/questionnaire/save` : null,
      generate: API_BASE_URL ? `${API_BASE_URL}/questionnaire/generate` : null,
    },
    // YAML endpoints (Vercel) - disabled in local development
    yaml: {
      list: API_BASE_URL ? `${API_BASE_URL}/yaml/list` : null,
      get: (file) => API_BASE_URL ? `${API_BASE_URL}/yaml/${file}` : null,
    },
    // Response endpoints (Vercel) - disabled in local development
    responses: {
      list: API_BASE_URL ? `${API_BASE_URL}/responses/list` : null,
      get: (id) => API_BASE_URL ? `${API_BASE_URL}/responses/${id}` : null,
      save: API_BASE_URL ? `${API_BASE_URL}/responses/save` : null,
      download: (id) => API_BASE_URL ? `${API_BASE_URL}/responses/download/${id}` : null,
    },
    // AI model endpoints (Railway)
    gemini: {
      list: `${API_BASE_URL}/gemini/list`, // Keep list on Vercel if needed
      ask: `${RAILWAY_API_BASE_URL}/api/gemini`,
    },
    openai: {
      ask: `${RAILWAY_API_BASE_URL}/api/openai`,
    },
    deepseek: {
      ask: `${RAILWAY_API_BASE_URL}/api/deepseek`,
    },
    anthropic: {
      ask: `${RAILWAY_API_BASE_URL}/api/anthropic`,
    },
    groq: {
      ask: `${RAILWAY_API_BASE_URL}/api/groq`,
    }
  }
};
