import React from 'react';
import { useAuth } from '../context/AuthContext';

function StorageDebugger() {
  const { StorageManager } = useAuth();

  const handleClearUserData = () => {
    if (window.confirm('Clear user-specific cached data? This will remove company profiles, questionnaire responses, and other cached data.')) {
      StorageManager.clearUserSpecificData();
      alert('User-specific data cleared! Please refresh the page.');
    }
  };

  const handleClearAllStorage = () => {
    if (window.confirm('Clear ALL storage? This will remove everything including authentication tokens and you will need to log in again.')) {
      StorageManager.clearAll();
      alert('All storage cleared! Please refresh the page and log in again.');
    }
  };

  const handleShowStorageInfo = () => {
    const localStorageItems = [];
    const sessionStorageItems = [];
    
    // Get localStorage items
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      localStorageItems.push({
        key,
        size: localStorage.getItem(key)?.length || 0
      });
    }
    
    // Get sessionStorage items
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      sessionStorageItems.push({
        key,
        size: sessionStorage.getItem(key)?.length || 0
      });
    }
    
    console.log('📊 Storage Information:');
    console.log('localStorage items:', localStorageItems);
    console.log('sessionStorage items:', sessionStorageItems);
    
    alert(`Storage Info (check console for details):
    
localStorage: ${localStorageItems.length} items
sessionStorage: ${sessionStorageItems.length} items

Check browser console for detailed breakdown.`);
  };

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <h3 className="text-lg font-semibold text-yellow-800 mb-2">🔧 Storage Debugger</h3>
      <p className="text-sm text-yellow-700 mb-3">
        If you're experiencing slow loading times, try clearing cached data:
      </p>
      
      <div className="space-y-2">
        <button
          onClick={handleShowStorageInfo}
          className="w-full bg-blue-500 text-white px-3 py-2 rounded text-sm hover:bg-blue-600 transition"
        >
          📊 Show Storage Info
        </button>
        
        <button
          onClick={handleClearUserData}
          className="w-full bg-orange-500 text-white px-3 py-2 rounded text-sm hover:bg-orange-600 transition"
        >
          🧹 Clear User Data Cache
        </button>
        
        <button
          onClick={handleClearAllStorage}
          className="w-full bg-red-500 text-white px-3 py-2 rounded text-sm hover:bg-red-600 transition"
        >
          🗑️ Clear All Storage (Logout)
        </button>
      </div>
      
      <p className="text-xs text-yellow-600 mt-2">
        💡 Tip: If pages are loading slowly, try "Clear User Data Cache" first.
      </p>
    </div>
  );
}

export default StorageDebugger;
