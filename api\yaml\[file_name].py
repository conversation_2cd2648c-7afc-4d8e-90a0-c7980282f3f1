#!/usr/bin/env python3
import json
import os
import sys
import yaml
from utils import get_public_dir

class YamlGetHandler:
    def __init__(self):
        pass
    
    def do_GET(self):
        """Handle GET request for a specific YAML file"""
        try:
            # Get file name from command line arguments
            if len(sys.argv) < 2:
                self.send_error_response(400, "File name is required")
                return
            
            file_name = sys.argv[1]
            public_dir = get_public_dir()
            file_path = os.path.join(public_dir, file_name)
            
            if not os.path.exists(file_path):
                self.send_error_response(404, "YAML file not found")
                return
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    yaml_content = yaml.safe_load(f)
                
                self.send_success_response(yaml_content)
            except Exception as e:
                self.send_error_response(500, f"Error reading YAML file: {str(e)}")
                
        except Exception as e:
            self.send_error_response(500, str(e))
    
    def send_success_response(self, data):
        """Send a successful response"""
        print("Content-Type: application/json")
        print("Access-Control-Allow-Origin: *")
        print("Access-Control-Allow-Methods: GET")
        print("Access-Control-Allow-Headers: Content-Type")
        print()
        print(json.dumps(data))
    
    def send_error_response(self, status_code, message):
        """Send an error response"""
        print(f"Status: {status_code}")
        print("Content-Type: application/json")
        print("Access-Control-Allow-Origin: *")
        print("Access-Control-Allow-Methods: GET")
        print("Access-Control-Allow-Headers: Content-Type")
        print()
        print(json.dumps({"error": message}))

if __name__ == "__main__":
    handler = YamlGetHandler()
    handler.do_GET()
